rules_version = '2';

// Firebase Storage Security Rules
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload category icons
    match /category_icons/{allPaths=**} {
      allow read: if true; // Allow public read access for category icons
      allow write: if request.auth != null && 
                      request.resource.size < 5 * 1024 * 1024 && // Max 5MB
                      request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Allow authenticated users to upload product images
    match /product_images/{allPaths=**} {
      allow read: if true; // Allow public read access for product images
      allow write: if request.auth != null && 
                      request.resource.size < 10 * 1024 * 1024 && // Max 10MB
                      request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Allow authenticated users to upload profile images
    match /profile_images/{allPaths=**} {
      allow read: if true; // Allow public read access for profile images
      allow write: if request.auth != null && 
                      request.resource.size < 5 * 1024 * 1024 && // Max 5MB
                      request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Allow authenticated users to upload post images
    match /post_images/{allPaths=**} {
      allow read: if true; // Allow public read access for post images
      allow write: if request.auth != null && 
                      request.resource.size < 10 * 1024 * 1024 && // Max 10MB
                      request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
